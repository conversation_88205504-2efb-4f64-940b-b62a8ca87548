#!/usr/bin/env python3

# Test script for tmp.py functionality
# This demonstrates how to run the script with the provided file paths

import subprocess
import sys

def test_tmp_script():
    """Test the tmp.py script with the provided file paths."""
    
    # File paths as provided by the user
    cna_merged_file = "/Users/<USER>/Desktop/002-RLGS2-primary_combine_intersected_results_gain.tsv"
    cnvkit_cns_file = "/Users/<USER>/Desktop/RLGS2-primary-primary.call.cns"
    coral_input_bed_file = "/Users/<USER>/Desktop/results.tsv"
    
    # Command to run tmp.py
    cmd = [
        "python3", "tmp.py",
        "--cna_merged_file", cna_merged_file,
        "--cnvkit_cns_file", cnvkit_cns_file,
        "--coral_input_bed_file", coral_input_bed_file
    ]
    
    print("Running command:")
    print(" ".join(cmd))
    print()
    
    try:
        # Run the script
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("STDOUT:")
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        print("Script completed successfully!")
        
    except subprocess.CalledProcessError as e:
        print(f"Script failed with return code {e.returncode}")
        print("STDOUT:")
        print(e.stdout)
        print("STDERR:")
        print(e.stderr)
    except FileNotFoundError:
        print("Error: Could not find python3 or tmp.py")
        print("Make sure you're running this from the directory containing tmp.py")

if __name__ == "__main__":
    test_tmp_script()
