# Alec Bahcheli

import argparse, time
from collections import defaultdict
import pandas as pd

help_message = '''
This script merges overlapping genomic intervals, keeping ones with highest copy number.
Input file is expected to be sorted by chromosome and start position.
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--cna_merged_file', type=str, required=True,
                        help='Merged CNA file from short read consensus')
    parser.add_argument('--cnvkit_cns_file', type=str, required=True,
                        help='CNVkit CNS file')
    parser.add_argument('--coral_input_bed_file', type=str, required=True,
                        help='Coral input bed file for nanopore ecDNA analysis')
    
    return parser.parse_args()

def merge_overlapping_intervals(cna_merged_file, minimum_cn=6):
    """Merge overlapping intervals by taking min start and max end positions, and max copy number."""
    # read as df
    df = pd.read_csv(cna_merged_file, sep='\t', header=None)
    df.columns = ['chr', 'start', 'end', 'classification', 'copy_number']

    # add chr to chromosome
    df['chr'] = 'chr' + df['chr'].astype('str')

    # subset to minimum cn
    df = df[df['copy_number'] >= minimum_cn]

    # Sort by chromosome and start
    df = df.sort_values(by=['chr', 'start']).reset_index(drop=True)

    merged = []
    current_group = []

    for _, row in df.iterrows():
        if not current_group:
            current_group.append(row)
            continue

        last = current_group[-1]

        # Overlap check: current row overlaps any interval in current group
        if row['chr'] == last['chr'] and row['start'] <= max(r['end'] for r in current_group):
            current_group.append(row)
        else:
            # Create merged interval with min start, max end, and max copy number
            merged_interval = {
                'chr': current_group[0]['chr'],
                'start': min(r['start'] for r in current_group),
                'end': max(r['end'] for r in current_group),
                'copy_number': max(r['copy_number'] for r in current_group)
            }
            merged.append(merged_interval)
            current_group = [row]

    # Don't forget the last group
    if current_group:
        merged_interval = {
            'chr': current_group[0]['chr'],
            'start': min(r['start'] for r in current_group),
            'end': max(r['end'] for r in current_group),
            'copy_number': max(r['copy_number'] for r in current_group)
        }
        merged.append(merged_interval)

    # convert to dataframe
    return pd.DataFrame(merged)


def cnvkit_to_bed(cnvkit_cns_file):
    """Convert CNVkit CNS file to bed format with chr, start, stop, cn columns."""
    # Read CNVkit CNS file
    df = pd.read_csv(cnvkit_cns_file, sep='\t')

    # Extract relevant columns and rename for bed format
    # Assuming CNVkit CNS file has columns: chromosome, start, end, cn (or similar)
    # Common CNVkit column names: chromosome, start, end, log2, cn
    bed_df = df[['chromosome', 'start', 'end', 'cn']].copy()
    bed_df.columns = ['chr', 'start', 'stop', 'cn']

    # Ensure chromosome names have 'chr' prefix
    if not bed_df['chr'].astype(str).str.startswith('chr').all():
        bed_df['chr'] = 'chr' + bed_df['chr'].astype(str)

    # Sort by chromosome and start position
    bed_df = bed_df.sort_values(by=['chr', 'start']).reset_index(drop=True)

    return bed_df


def embed_merged_intervals_in_bed(bed_df, merged_intervals_df):
    """
    Embed merged intervals into bed file, modifying bed intervals to avoid overlaps.
    For each chromosome, ensure merged intervals don't overlap with bed intervals.
    """
    result_intervals = []

    # Process each chromosome separately
    for chrom in bed_df['chr'].unique():
        chrom_bed = bed_df[bed_df['chr'] == chrom].copy().sort_values('start').reset_index(drop=True)
        chrom_merged = merged_intervals_df[merged_intervals_df['chr'] == chrom].copy().sort_values('start').reset_index(drop=True)

        if chrom_merged.empty:
            # No merged intervals for this chromosome, add all bed intervals as-is
            for _, row in chrom_bed.iterrows():
                result_intervals.append({
                    'chr': row['chr'],
                    'start': row['start'],
                    'stop': row['stop'],
                    'cn': row['cn']
                })
            continue

        # Create a list to track all intervals for this chromosome
        all_intervals = []

        # Add merged intervals (these take priority)
        for _, row in chrom_merged.iterrows():
            all_intervals.append({
                'chr': row['chr'],
                'start': row['start'],
                'stop': row['end'],  # Note: merged intervals use 'end', bed uses 'stop'
                'cn': row['copy_number'],
                'type': 'merged'
            })

        # Process bed intervals, splitting them if they overlap with merged intervals
        for _, bed_row in chrom_bed.iterrows():
            bed_start = bed_row['start']
            bed_stop = bed_row['stop']
            bed_cn = bed_row['cn']

            # Check for overlaps with merged intervals
            overlapping_merged = []
            for merged_row in chrom_merged.itertuples():
                if not (bed_stop <= merged_row.start or bed_start >= merged_row.end):
                    overlapping_merged.append((merged_row.start, merged_row.end))

            if not overlapping_merged:
                # No overlaps, add bed interval as-is
                all_intervals.append({
                    'chr': bed_row['chr'],
                    'start': bed_start,
                    'stop': bed_stop,
                    'cn': bed_cn,
                    'type': 'bed'
                })
            else:
                # Split bed interval around overlapping merged intervals
                overlapping_merged.sort()  # Sort by start position

                current_pos = bed_start
                for merged_start, merged_end in overlapping_merged:
                    # Add segment before merged interval (if any)
                    if current_pos < merged_start:
                        all_intervals.append({
                            'chr': bed_row['chr'],
                            'start': current_pos,
                            'stop': merged_start,
                            'cn': bed_cn,
                            'type': 'bed'
                        })
                    current_pos = max(current_pos, merged_end)

                # Add remaining segment after last merged interval (if any)
                if current_pos < bed_stop:
                    all_intervals.append({
                        'chr': bed_row['chr'],
                        'start': current_pos,
                        'stop': bed_stop,
                        'cn': bed_cn,
                        'type': 'bed'
                    })

        # Add all intervals for this chromosome to result
        result_intervals.extend(all_intervals)

    # Convert to DataFrame and sort
    result_df = pd.DataFrame(result_intervals)
    if not result_df.empty:
        result_df = result_df.sort_values(['chr', 'start']).reset_index(drop=True)
        # Remove the 'type' column as it was just for processing
        result_df = result_df[['chr', 'start', 'stop', 'cn']]

    return result_df
    

def main():
    print('coral_gain_regions_preparation.py')
    t1 = time.time()

    args = parse_args()

    # Step 1: Merge overlapping intervals from CNA merged file (for regions > minimum_cn)
    print("Merging overlapping intervals from CNA merged file...")
    merged_intervals_df = merge_overlapping_intervals(args.cna_merged_file)
    print(f"Found {len(merged_intervals_df)} merged intervals")

    # Step 2: Convert CNVkit CNS file to bed format
    print("Converting CNVkit CNS file to bed format...")
    bed_df = cnvkit_to_bed(args.cnvkit_cns_file)
    print(f"Converted {len(bed_df)} intervals from CNVkit CNS file")

    # Step 3: Embed merged intervals into bed file, avoiding overlaps
    print("Embedding merged intervals into bed file...")
    final_df = embed_merged_intervals_in_bed(bed_df, merged_intervals_df)
    print(f"Final result contains {len(final_df)} intervals")

    # Step 4: Save the result to coral input bed file
    print(f"Saving results to {args.coral_input_bed_file}...")
    final_df.to_csv(args.coral_input_bed_file, sep='\t', index=False, header=False)
    print("Results saved successfully")

    print(f"Runtime: {round(time.time() - t1, 2)} seconds")
    print('coral_gain_regions_preparation.py COMPLETE')

if __name__ == "__main__":
    main()
