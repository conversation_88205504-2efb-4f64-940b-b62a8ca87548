# Alec Bahcheli

import argparse, time
from collections import defaultdict
import pandas as pd

help_message = '''
This script merges overlapping genomic intervals, keeping ones with highest copy number.
Input file is expected to be sorted by chromosome and start position.
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--cna_merged_file', type=str, required=True,
                        help='Merged CNA file from short read consensus')
    parser.add_argument('--cnvkit_cns_file', type=str, required=True,
                        help='CNVkit CNS file')
    parser.add_argument('--coral_input_bed_file', type=str, required=True,
                        help='Coral input bed file for nanopore ecDNA analysis')
    
    return parser.parse_args()

def merge_overlapping_intervals(cna_merged_file, minimum_cn=6):
    """Merge overlapping intervals by taking min start and max end positions, and max copy number."""
    # read as df
    df = pd.read_csv(cna_merged_file, sep='\t', header=None)
    df.columns = ['chr', 'start', 'end', 'classification', 'copy_number']

    # add chr to chromosome
    df['chr'] = 'chr' + df['chr'].astype('str')    
    
    # subset to minimum cn
    df = df[df['copy_number'] >= minimum_cn]
    
    # Sort by chromosome and start
    df = df.sort_values(by=['chr', 'start']).reset_index(drop=True)

    merged = []
    current_group = []

    for _, row in df.iterrows():
        if not current_group:
            current_group.append(row)
            continue

        last = current_group[-1]
        
        # Overlap check: current row overlaps any interval in current group
        if row['chr'] == last['chr'] and row['start'] <= max(r['end'] for r in current_group):
            current_group.append(row)
        else:
            # Create merged interval with min start, max end, and max copy number
            merged_interval = {
                'chr': current_group[0]['chr'],
                'start': min(r['start'] for r in current_group),
                'end': max(r['end'] for r in current_group),
                'copy_number': max(r['copy_number'] for r in current_group)
            }
            merged.append(merged_interval)
            current_group = [row]

    # Don't forget the last group
    if current_group:
        merged_interval = {
            'chr': current_group[0]['chr'],
            'start': min(r['start'] for r in current_group),
            'end': max(r['end'] for r in current_group),
            'copy_number': max(r['copy_number'] for r in current_group)
        }
        merged.append(merged_interval)
    
    # convert to dataframe
    df = pd.DataFrame(merged)
    

def main():
    print('coral_gain_regions_preparation.py')
    t1 = time.time()
    
    args = parse_args()
    
    # Merge overlapping intervals for each graph file
    merge_overlapping_intervals(args.cna_merged_file, args.cnvkit_cns_file, args.coral_input_bed_file)
    
    print(f"Runtime: {round(time.time() - t1, 2)} seconds")
    print('coral_gain_regions_preparation.py COMPLETE')

if __name__ == "__main__":
    main()
